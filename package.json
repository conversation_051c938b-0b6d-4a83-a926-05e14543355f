{"name": "ruet-materials-club-website", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate:types": "PAYLOAD_CONFIG_PATH=src/payload.config.ts payload generate:types", "prepare": "husky"}, "dependencies": {"@payloadcms/db-vercel-postgres": "^3.35.1", "@payloadcms/next": "^3.35.1", "@payloadcms/richtext-lexical": "^3.35.1", "@payloadcms/storage-vercel-blob": "^3.35.1", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@react-hook/throttle": "^2.2.0", "@react-three/drei": "^10.0.0-rc.1", "@react-three/fiber": "^9.0.0-rc.6", "@tailwindcss/postcss": "^4.0.4", "@types/three": "^0.173.0", "@vercel/blob": "^1.0.0", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "disable-scroll": "^0.6.0", "graphql": "^16.10.0", "hamburger-react": "^2.5.2", "lucide-react": "^0.475.0", "motion": "^12.4.1", "next": "15.1.6", "package-manager-detector": "^1.2.0", "payload": "^3.35.1", "prettier-plugin-organize-imports": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-swipeable": "^7.0.2", "sharp": "^0.34.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "three": "^0.173.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.17", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "eslint": "^9.19.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.0.1", "eslint-plugin-react-compiler": "^19.0.0-beta-714736e-20250131", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.5.1", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.4", "typescript": "^5.7.3"}, "packageManager": "bun@1.2.0"}