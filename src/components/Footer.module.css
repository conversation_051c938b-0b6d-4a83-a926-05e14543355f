.footer {
  background-color: var(--c1);
  filter: drop-shadow(0 0 1rem var(--c2));
}

.footer::before {
  --s: 0.65rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 80'%3E%3Cpath d='M -100,124.23 L 100,8.76 L 300,124.23' stroke='%23e4e5e4' fill='%23fbfdfc' strokeWidth='15' /%3E%3C/svg%3E");
  background-size: calc(2 * var(--s)) calc(0.8 * var(--s));
  background-repeat: repeat-x;
  position: absolute;
  top: calc(-0.8 * var(--s));
  content: "";
  display: block;
  height: calc(0.8 * var(--s));
  width: 100%;
  z-index: -1;
  filter: drop-shadow(0 -2px 0 var(--c2));
}
